#!/usr/bin/env python3
"""
Simple HTTP debug server to test the indicator API endpoint.
This helps debug the content type issue with the Spring Boot Feign client.

To run: python debug_indicator_api.py
Then test with: curl -X POST http://localhost:5000/larsson/calculate -H "Content-Type: application/json" -d '[]'
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import logging
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@app.route('/monitoring/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    logger.info("Health check requested")
    return "OK", 200

@app.route('/larsson/calculate', methods=['POST'])
def calculate_indicators():
    """Debug endpoint to inspect the incoming request"""
    logger.info("=== INCOMING REQUEST DEBUG ===")
    logger.info(f"Method: {request.method}")
    logger.info(f"Content-Type: {request.content_type}")
    logger.info(f"Headers: {dict(request.headers)}")
    
    # Log raw data
    raw_data = request.get_data()
    logger.info(f"Raw data length: {len(raw_data)} bytes")
    logger.info(f"Raw data (first 200 chars): {raw_data[:200]}")
    
    # Try to parse JSON
    try:
        if request.content_type == 'application/json':
            json_data = request.get_json()
            logger.info(f"Parsed JSON successfully: {type(json_data)}")
            logger.info(f"JSON data: {json_data}")
            
            # Return mock response
            mock_response = [
                {
                    "symbol": "BTC",
                    "timestamp": "2024-01-01T00:00:00Z",
                    "rsi": 65.5,
                    "macd": 0.123,
                    "signal": 0.098
                }
            ]
            return jsonify(mock_response), 200
        else:
            logger.error(f"Unsupported Content-Type: {request.content_type}")
            return {"error": f"Unsupported Media Type: {request.content_type}"}, 415
            
    except Exception as e:
        logger.error(f"Error parsing request: {e}")
        return {"error": f"Bad Request: {str(e)}"}, 400

@app.route('/', methods=['GET'])
def root():
    """Root endpoint"""
    return {
        "service": "Debug Indicator API",
        "endpoints": {
            "health": "/monitoring/health",
            "calculate": "/larsson/calculate"
        }
    }

if __name__ == '__main__':
    logger.info("Starting debug indicator API server on port 5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
