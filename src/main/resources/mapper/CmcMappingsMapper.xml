<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.CmcMappingsMapper">
    <insert id="insert">
        INSERT INTO crypto_data.cmc_mappings
        (cryptocurrency_id, symbol, name, slug, rank, is_active, first_historical_data, last_historical_data, platform,
        created_at,
        updated_at)
        VALUES
        <foreach collection="mappings" item="crypto" separator=",">
            (#{crypto.id}, #{crypto.symbol}, #{crypto.name}, #{crypto.slug}, #{crypto.rank},
            #{crypto.isActive},
            #{crypto.firstHistoricalData}, #{crypto.lastHistoricalData}, #{crypto.platform}::jsonb, NOW(), NOW())
        </foreach>
    </insert>

    <select id="findBySymbol" resultType="com.trading.financialindicatordaemon.client.CryptocurrencyMapping">
        SELECT cryptocurrency_id     AS id,
               rank,
               name,
               symbol,
               slug,
               is_active             AS isactive,
               first_historical_data AS firsthistoricaldata,
               last_historical_data  AS lasthistoricaldata,
               platform
        FROM crypto_data.cmc_mappings
        WHERE symbol = #{symbol}
        ORDER BY rank ASC
        LIMIT 1
    </select>

</mapper>
