package com.trading.financialindicatordaemon.typehandler;

import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes(CryptoCandleHistoricalQuote.Quote.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class QuoteTypeHandler extends BaseTypeHandler<CryptoCandleHistoricalQuote.Quote> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, CryptoCandleHistoricalQuote.Quote parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");
            jsonObject.setValue(objectMapper.writeValueAsString(parameter));
            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting Quote to JSON", e);
        }
    }

    @Override
    public CryptoCandleHistoricalQuote.Quote getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public CryptoCandleHistoricalQuote.Quote getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public CryptoCandleHistoricalQuote.Quote getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private CryptoCandleHistoricalQuote.Quote parseJson(String json) throws SQLException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, CryptoCandleHistoricalQuote.Quote.class);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error parsing JSON to Quote", e);
        }
    }
}
