package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Mapper
public interface CmcCandleDataMapper {

    void insert(@Param("cryptoCurrencySymbol") String cryptoCurrencySymbol,
                @Param("conversionCurrency") String conversionCurrency,
                @Param("quotes") List<CryptoCandleHistoricalQuote> quotes);

    List<CryptoCandleHistoricalQuote> findBySymbolAndConversionCurrency(
            @Param("cryptoCurrencySymbol") String cryptoCurrencySymbol,
            @Param("conversionCurrency") String conversionCurrency);

    Optional<LocalDateTime> findLatestCloseTimestamp(@Param("symbol") String symbol,
                                                     @Param("conversionCurrency") String conversionCurrency);

    List<CryptoCandleHistoricalQuoteSymbolAndConversionCurrency> findAllSymbolAndConversionCurrency();

}
