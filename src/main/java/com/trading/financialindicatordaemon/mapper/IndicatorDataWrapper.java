package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.IndicatorData;

import java.util.List;

public class IndicatorDataWrapper {
    private String symbol;
    private String conversionCurrency;
    private List<IndicatorData> indicatorValues;

    public String getSymbol() {
        return symbol;
    }

    public String getConversionCurrency() {
        return conversionCurrency;
    }

    public List<IndicatorData> getIndicatorValues() {
        return indicatorValues;
    }

    public void setIndicatorValues(List<IndicatorData> indicatorValues) {
        this.indicatorValues = indicatorValues;
    }
}
