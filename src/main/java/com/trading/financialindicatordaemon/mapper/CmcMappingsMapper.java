package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.CryptocurrencyMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface CmcMappingsMapper {

    Optional<CryptocurrencyMapping> findBySymbol(@Param("symbol") String symbol);

    void insert(@Param("mappings") List<CryptocurrencyMapping> mappings);

}
