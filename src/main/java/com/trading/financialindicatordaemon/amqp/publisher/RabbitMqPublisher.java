package com.trading.financialindicatordaemon.amqp.publisher;

import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class RabbitMqPublisher {

    private static final Logger logger = LoggerFactory.getLogger(RabbitMqPublisher.class);

    private final RabbitTemplate rabbitTemplate;
    private final ObjectMapper objectMapper;

    public RabbitMqPublisher(RabbitTemplate rabbitTemplate, ObjectMapper objectMapper) {
        this.rabbitTemplate = rabbitTemplate;
        this.objectMapper = objectMapper;
    }

    public void mineUsdDataBySymbols(SymbolsMessage message) {
        publishMessage(RabbitMqConfig.MINE_USD_DATA_BY_SYMBOLS, message);
    }

    public void publishCreateIndicatorData() {
        publishMessage(RabbitMqConfig.CREATE_INDICATOR_DATA, new HashMap<>());
    }

    public void publishMessage(String queueName, Object payload) {
        try {
            String message = objectMapper.writeValueAsString(payload);
            int messageSize = message.getBytes().length;

            logger.info("📤 Publishing message to queue: {} ({} bytes)", queueName, messageSize);
            long startTime = System.currentTimeMillis();

            rabbitTemplate.convertAndSend(queueName, payload);

            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("✅ Message published successfully in {}ms", processingTime);

        } catch (JsonProcessingException e) {
            logger.error("❌ Failed to serialize message payload for queue: {}", queueName, e);
            throw new RuntimeException("Failed to serialize message", e);
        } catch (Exception e) {
            logger.error("❌ Failed to publish message to queue: {}", queueName, e);
            throw new RuntimeException("Failed to publish message", e);
        }
    }

}
