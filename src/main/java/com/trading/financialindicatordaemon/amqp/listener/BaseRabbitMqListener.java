package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.config.AppConfig;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.service.DataMiningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import java.io.IOException;

public abstract class BaseRabbitMqListener {

    private static final Logger logger = LoggerFactory.getLogger(BaseRabbitMqListener.class);

    protected final DataMiningService dataMiningService;

    public BaseRabbitMqListener(DataMiningService dataMiningService) {
        this.dataMiningService = dataMiningService;
    }

    protected void handleSymbolsMessage(SymbolsMessage message, String currency,
                                        Channel channel, long deliveryTag, Message amqpMessage) {
        try {
            channel.basicAck(deliveryTag, false);

            if (message.getSymbols() != null && !message.getSymbols().isEmpty()) {
                logger.info("Processing {} symbols for {}", message.getSymbols().size(), currency);
                dataMiningService.mineSymbols(message.getSymbols(),
                        "USD".equals(currency) ? AppConfig.USD_CURRENCY_ID : AppConfig.BTC_CURRENCY_ID);
            }
        } catch (Exception e) {
            logger.error("Message processing failed", e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }

    protected void handleSimpleMessage(String messageType, Channel channel, long deliveryTag,
                                       Message amqpMessage, Runnable processor) {
        try {
            channel.basicAck(deliveryTag, false);
            processor.run();
            logger.info("Processed {} message", messageType);
        } catch (Exception e) {
            logger.error("Failed to process {} message", messageType, e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }
}

