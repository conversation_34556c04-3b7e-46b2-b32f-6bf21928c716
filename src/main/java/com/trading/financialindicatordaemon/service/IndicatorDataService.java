package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.client.CalculateIndicatorsRequest;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.IndicatorApiClient;
import com.trading.financialindicatordaemon.client.IndicatorData;
import com.trading.financialindicatordaemon.mapper.IndicatorDataMapper;
import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

@Service
public class IndicatorDataService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorDataService.class);

    private final IndicatorApiClient indicatorApiClient;
    private final CmcCandleDataService cmcCandleDataService;
    private final IndicatorDataMapper indicatorDataMapper;

    public IndicatorDataService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                IndicatorApiClient indicatorApiClient,
                                CmcCandleDataService cmcCandleDataService,
                                IndicatorDataMapper indicatorDataMapper) {
        this.indicatorApiClient = indicatorApiClient;
        this.cmcCandleDataService = cmcCandleDataService;
        this.indicatorDataMapper = indicatorDataMapper;
    }

    public void calculate(String symbol, String conversionCurrency) {
        logger.info("Sending calculation request to indicator API");

        List<CryptoCandleHistoricalQuote> quotes = cmcCandleDataService.find(symbol, conversionCurrency);
        List<CalculateIndicatorsRequest> requests = quotes.stream()
                .sorted(Comparator.comparing(q -> q.getQuote().getTimestamp()))
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getQuote().getClose());
                            request.setHigh(quote.getQuote().getHigh());
                            request.setLow(quote.getQuote().getLow());
                            request.setMarketCap(quote.getQuote().getMarketCap());
                            request.setName(quote.getQuote().getName());
                            request.setOpen(quote.getQuote().getOpen());
                            request.setTimestamp(quote.getQuote().getTimestamp());
                            request.setVolume(quote.getQuote().getVolume());
                            return request;
                        }
                ).toList();
        ResponseEntity<List<IndicatorData>> response = indicatorApiClient.calculateIndicators(requests);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to calculate indicators: " + response.getStatusCode());
        }

        insert(symbol, conversionCurrency, response.getBody());
    }

    public List<IndicatorData> find(String symbol, String conversionCurrency) {
        IndicatorDataWrapper wrapper = indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency);

        if (wrapper != null && wrapper.getIndicatorValues() != null) {
            return wrapper.getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

    void insert(String symbol, String conversionCurrency, List<IndicatorData> data) {
        indicatorDataMapper.insert(symbol, conversionCurrency, data);
    }

    public List<IndicatorDataWrapper> findAll() {
        return indicatorDataMapper.findAll();
    }

}
