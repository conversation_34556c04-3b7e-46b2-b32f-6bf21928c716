package com.trading.financialindicatordaemon.config;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = "app")
@Validated
public record AppConfig(
        CmcConfig cmc,
        IndicatorApiConfig indicatorApi,
        @Positive Integer amountOfCoinsToGetByRanking
) {

    public static final int USD_CURRENCY_ID = 2781;
    public static final int BTC_CURRENCY_ID = 1;

    public record CmcConfig(
            @NotBlank String apiKey,
            ThrottleConfig throttle,
            String symbolOverrides
    ) {
    }

    public record ThrottleConfig(
            @Positive Integer min,
            @Positive Integer max
    ) {
    }

    public record IndicatorApiConfig(
            @NotBlank String host
    ) {
    }

}
