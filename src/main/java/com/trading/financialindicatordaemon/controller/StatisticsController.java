package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.IndicatorDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Tag(name = "Statistics", description = "Cryptocurrency statistics API")
public class StatisticsController {
    private static final org.slf4j.Logger logger = getLogger(StatisticsController.class);

    private final IndicatorDataService indicatorDataService;

    public StatisticsController(IndicatorDataService indicatorDataService) {
        this.indicatorDataService = indicatorDataService;
    }

    @Operation(
            summary = "Get cryptocurrency statistics",
            description = "Retrieve cryptocurrency statistics for both USD and BTC currencies"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Statistics retrieved successfully",
                    content = @Content(
                            mediaType = "application/json"
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content
            )
    })
    @GetMapping("/api/v1/crypto/statistics")
    public ResponseEntity<List<IndicatorDataWrapper>> getCryptoStatistics() {
        logger.info("Fetching cryptocurrency statistics");
        List<IndicatorDataWrapper> all = indicatorDataService.findAll();
        logger.info("Found {} statistics records", all.size());
        return ResponseEntity.ok(all);
    }

}
