package com.trading.financialindicatordaemon.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "indicator-api", url = "${app.indicator-api.host}")
public interface IndicatorApiClient {

    @GetMapping("/monitoring/health")
    ResponseEntity<String> checkHealth();

    @PostMapping("/larsson/calculate")
    ResponseEntity<List<IndicatorData>> calculateIndicators(@RequestBody List<CalculateIndicatorsRequest> data);

}
