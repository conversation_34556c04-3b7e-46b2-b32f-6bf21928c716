package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.IndicatorData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class IndicatorDataServiceTest extends BaseTest {

    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;

    @Test
    public void calculate_shouldStore() {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), 2781);
        indicatorDataService.calculate("SOL", "USD");
        List<IndicatorData> indicatorData = indicatorDataService.find("SOL", "USD");
        assertThat(indicatorData).isNotNull();
        assertThat(indicatorData).isNotEmpty();
        assertThat(indicatorData.size()).isEqualTo(1893);
    }

    @Test
    public void insert_shouldUpdateExisting() {
        IndicatorData initial = new IndicatorData();
        initial.setTimestamp("2024-01-05T00:00:00Z");
        indicatorDataService.insert("SOL", "USD", List.of(initial));
        List<IndicatorData> indicatorData = indicatorDataService.find("SOL", "USD");
        assertThat(indicatorData.getFirst().getTimestamp()).isEqualTo("2024-01-05T00:00:00Z");

        IndicatorData updated = new IndicatorData();
        updated.setTimestamp("2024-01-06T00:00:00Z");

        indicatorDataService.insert("SOL", "USD", List.of(updated));
        assertThat(indicatorDataService.find("SOL", "USD").getFirst().getTimestamp()).isEqualTo("2024-01-06T00:00:00Z");
    }

}
