package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuotes;
import com.trading.financialindicatordaemon.client.CryptocurrencyMapping;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;


public class CoinMarketCapServiceTest extends BaseTest {

    @Autowired
    private CoinMarketCapService coinMarketCapService;

    @Test
    public void getMappings_shouldReturnMappings() {
        List<CryptocurrencyMapping> mappings = coinMarketCapService.getMappings();
        assertThat(mappings).isNotEmpty();
    }

    @Test
    public void findQuotes_shouldReturnQuotes() {
        String start = "1579110719";
        String end = "1594662719";
        assertThat(Integer.parseInt(end) - Integer.parseInt(start)).isEqualTo(15552000);

        CryptoCandleHistoricalQuotes quotes = coinMarketCapService.findQuotes(5426, 1, start, end);
        assertThat(quotes).isNotNull();
        assertThat(quotes.getQuotes()).isNotEmpty();
        assertThat(quotes.getQuotes()).hasSize(180);
    }

}
