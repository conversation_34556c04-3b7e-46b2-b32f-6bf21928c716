package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.service.DataMiningService;
import com.trading.financialindicatordaemon.service.IndicatorDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static com.trading.financialindicatordaemon.config.AppConfig.USD_CURRENCY_ID;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
public class StatisticsControllerTest extends BaseTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;

    @Test
    public void getCryptoStatistics_shouldReturnDataWhenExists() throws Exception {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        indicatorDataService.calculate("SOL", "USD");

        // When & Then
        mockMvc.perform(get("/api/v1/crypto/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[*].symbol").exists())
                .andExpect(jsonPath("$[*].conversionCurrency").exists())
                .andExpect(jsonPath("$[*].indicatorValues").exists());
    }

}
