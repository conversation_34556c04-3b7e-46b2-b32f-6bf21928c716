package com.trading.financialindicatordaemon.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
public class MockCoinMarketCapApiClient implements CoinMarketCapApiClient {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ResponseEntity<CryptocurrencyMappings> getCryptocurrencyMappings(String apiKey) {
        try {
            Map<String, Object> map = objectMapper.readValue(
                    getClass().getClassLoader().getResourceAsStream("data/mappings/mappings.json"),
                    Map.class
            );

            Object data = map.get("data");

            CryptocurrencyMappings mappings = objectMapper.convertValue(Map.of("data", data), CryptocurrencyMappings.class);
            return ResponseEntity.ok(mappings);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
